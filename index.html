<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>AR Image Target Video Player</title>
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v7.0.0/dist/aframe-extras.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.2.5/dist/mindar-image-aframe.prod.js"></script>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        font-family: 'Arial', sans-serif;
      }

      /* Startup Screen with White Background */
      .startup-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        z-index: 10000;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: opacity 0.5s ease-out;
      }

      .startup-screen.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .logo-container {
        text-align: center;
        margin-bottom: 50px;
      }

      .logo-text {
        font-size: 3em;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
        letter-spacing: 3px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .logo-subtitle {
        font-size: 1.2em;
        color: #666;
        margin-bottom: 40px;
        font-weight: 300;
      }

      .start-button {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
        border: none;
        padding: 18px 50px;
        font-size: 1.3em;
        font-weight: bold;
        border-radius: 50px;
        cursor: pointer;
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 2px;
        outline: none;
      }

      .start-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
      }

      .start-button:active {
        transform: translateY(-1px);
      }

      /* Loading Screen (Artivive Style) */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        z-index: 9999;
        display: none;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .loading-screen.show {
        display: flex;
      }

      .loading-content {
        text-align: center;
        color: #333;
      }

      .loading-title {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 20px;
        color: #333;
      }

      .loading-text {
        font-size: 1.2em;
        margin-bottom: 40px;
        color: #666;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 5px solid #e3e3e3;
        border-top: 5px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 30px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .progress-bar {
        width: 250px;
        height: 6px;
        background-color: #e3e3e3;
        border-radius: 3px;
        overflow: hidden;
        margin: 20px auto;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        width: 0%;
        transition: width 0.3s ease;
        border-radius: 3px;
      }

      .loading-steps {
        font-size: 0.9em;
        color: #888;
        margin-top: 20px;
      }

      /* AR Scene Styles */
      #ar-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }

      .a-canvas {
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
      }

      .a-enter-vr, .a-enter-ar {
        display: none !important;
      }

      /* Mobile optimizations */
      @media (max-width: 768px) {
        .logo-text {
          font-size: 2.5em;
        }

        .start-button {
          padding: 15px 40px;
          font-size: 1.1em;
        }

        .loading-title {
          font-size: 1.8em;
        }

        .progress-bar {
          width: 200px;
        }
      }

      /* Ensure full screen on all devices */
      @media screen and (orientation: portrait) {
        .startup-screen, .loading-screen {
          height: 100vh;
          height: -webkit-fill-available;
        }
      }

      @media screen and (orientation: landscape) {
        .startup-screen, .loading-screen {
          height: 100vh;
          height: -webkit-fill-available;
        }
      }
    </style>
  </head>

  <body>
    <!-- Startup Screen with White Background -->
    <div class="startup-screen" id="startup-screen">
      <div class="logo-container">
        <div class="logo-text">AR VIEWER</div>
        <div class="logo-subtitle">Augmented Reality Experience</div>
      </div>
      <button class="start-button" onclick="startARExperience()">
        START EXPERIENCE
      </button>
    </div>

    <!-- Loading Screen (Artivive Style) -->
    <div class="loading-screen" id="loading-screen">
      <div class="loading-content">
        <div class="loading-title">Initializing AR</div>
        <div class="loading-spinner"></div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="loading-text" id="loading-text">Preparing camera...</div>
        <div class="loading-steps" id="loading-steps">Step 1 of 4</div>
      </div>
    </div>

    <!-- AR Scene (hidden initially) -->
    <div id="ar-container" style="display: none;">
      <a-scene
        mindar-image="imageTargetSrc: https://cdn.glitch.global/7a663fb2-a522-47b6-9e6a-f3acfe1d5a8f/targets.mind?v=1747280453573;
                      uiScanning: no;
                      filterMinCF: 0.0001;
                      filterBeta: 0.001;
                      missTolerance: 5;
                      warmupTolerance: 3;"
        vr-mode-ui="enabled: false"
        device-orientation-permission-ui="enabled: false"
        renderer="precision: high; antialias: true; alpha: true;"
        embedded
        style="width: 100%; height: 100%;"
      >
        <!-- Assets -->
        <a-assets>
          <video
            id="target-video"
            src="https://cdn.glitch.me/7a663fb2-a522-47b6-9e6a-f3acfe1d5a8f/RE_compiled%20animated.mp4?v=1747279561796"
            preload="auto"
            muted
            loop
            crossorigin="anonymous"
            playsinline
            webkit-playsinline>
          </video>
        </a-assets>

        <a-camera position="0 0 0" look-controls="enabled: false" wasd-controls="enabled: false"></a-camera>

        <!-- Image Target -->
        <a-entity mindar-image-target="targetIndex: 0" id="target-0">
          <a-video
            src="#target-video"
            position="0 0 0"
            rotation="0 0 0"
            width="1.6"
            height="0.9"
            material="shader: flat; transparent: true; alphaTest: 0.5"
            play="false">
          </a-video>
        </a-entity>
      </a-scene>
    </div>

    <script>
      let arInitialized = false;

      // Loading steps simulation (Artivive style)
      const loadingSteps = [
        { text: "Preparing camera...", duration: 1000 },
        { text: "Loading AR engine...", duration: 1500 },
        { text: "Downloading target data...", duration: 2000 },
        { text: "Initializing tracking...", duration: 1000 }
      ];

      function startARExperience() {
        const startupScreen = document.getElementById('startup-screen');
        const loadingScreen = document.getElementById('loading-screen');

        // Hide startup screen and show loading screen
        startupScreen.classList.add('hidden');
        setTimeout(() => {
          startupScreen.style.display = 'none';
          loadingScreen.classList.add('show');
          simulateLoading();
        }, 500);
      }

      function simulateLoading() {
        const progressFill = document.getElementById('progress-fill');
        const loadingText = document.getElementById('loading-text');
        const loadingStepsEl = document.getElementById('loading-steps');

        let currentStep = 0;
        let totalProgress = 0;

        function runStep() {
          if (currentStep >= loadingSteps.length) {
            // Loading complete, initialize AR
            initializeAR();
            return;
          }

          const step = loadingSteps[currentStep];
          loadingText.textContent = step.text;
          loadingStepsEl.textContent = `Step ${currentStep + 1} of ${loadingSteps.length}`;

          // Animate progress
          const stepProgress = 100 / loadingSteps.length;
          const targetProgress = (currentStep + 1) * stepProgress;

          const animateProgress = () => {
            if (totalProgress < targetProgress) {
              totalProgress += 2;
              progressFill.style.width = totalProgress + '%';
              requestAnimationFrame(animateProgress);
            }
          };

          animateProgress();

          setTimeout(() => {
            currentStep++;
            runStep();
          }, step.duration);
        }

        runStep();
      }

      async function initializeAR() {
        try {
          const loadingScreen = document.getElementById('loading-screen');
          const arContainer = document.getElementById('ar-container');

          // Request camera permission
          await navigator.mediaDevices.getUserMedia({
            video: { facingMode: "environment" }
          });

          // Hide loading screen and show AR
          loadingScreen.classList.remove('show');
          arContainer.style.display = 'block';

          // Set up AR event listeners
          setupAREventListeners();

          arInitialized = true;
          console.log("AR initialized successfully");

        } catch (error) {
          console.error("Failed to initialize AR:", error);
          document.getElementById('loading-text').textContent = "Camera access required. Please refresh and allow camera access.";
          document.getElementById('loading-steps').textContent = "Error occurred";
        }
      }

      function setupAREventListeners() {
        const video = document.querySelector("#target-video");
        const targetEntity = document.querySelector('[mindar-image-target]');

        if (targetEntity) {
          targetEntity.addEventListener("targetFound", function() {
            console.log("Target found!");
            video.play().catch(e => {
              console.error("Error playing video:", e);
            });
          });

          targetEntity.addEventListener("targetLost", function() {
            console.log("Target lost!");
            video.pause();
          });
        }

        // Add error handling for video
        if (video) {
          video.addEventListener('error', function(e) {
            console.error('Video error:', e);
          });
        }
      }

      // Initialize on page load
      document.addEventListener("DOMContentLoaded", function() {
        console.log("Page loaded, showing startup screen");
      });
    </script>
  </body>
</html>